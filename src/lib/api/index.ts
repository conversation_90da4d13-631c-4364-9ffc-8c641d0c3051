import type {
  SekaiInfoResponse,
  SekaiItem,
  CampaignBannerResponse,
  CampaignBannerItem,
  FandomResponse,
  FandomItem,
} from './types';

const SEKAI_API_BASE_URL = process.env.NEXT_PUBLIC_SEKAI_API_BASE_URL || 'http://127.0.0.1:7000';
const DISCOVER_API_BASE_URL = process.env.NEXT_PUBLIC_DISCOVER_API_BASE_URL || 'http://localhost:7700/api/v1/discover';

// 自定义错误类，用于 API 请求错误
export class ApiError extends Error {
  constructor(message: string, public status?: number, public response?: unknown) {
    super(message);
    this.name = 'ApiError';
  }
}

// 通用请求处理函数
async function request<T>(url: string, options?: RequestInit): Promise<T> {
  try {
    const response = await fetch(url, options);
    if (!response.ok) {
      let errorData;
      try {
        errorData = await response.json();
      } catch {
        // 如果响应体不是 JSON，或者解析失败
        errorData = { message: response.statusText };
      }
      throw new ApiError(
        `API request failed: ${response.status} ${response.statusText}`,
        response.status,
        errorData
      );
    }
    const data = await response.json();
    // 根据不同 API 的成功 code 进行判断
    if (data.code !== 200 && data.code !== 0) {
        throw new ApiError(data.message || data.msg || 'API returned an error', data.code, data);
    }
    return data;
  } catch (error) {
    if (error instanceof ApiError) {
      throw error;
    }
    // 网络错误或其他 fetch 导致的错误
    throw new ApiError(error instanceof Error ? error.message : 'An unknown network error occurred');
  }
}

/**
 * 获取 Sekai 信息
 * @param sekaiIds Sekai ID 数组
 */
export const fetchSekaiInfo = async (sekaiIds: number[]): Promise<SekaiItem[]> => {
  if (!sekaiIds || sekaiIds.length === 0) {
    return [];
  }
  const params = new URLSearchParams({ sekai_ids: sekaiIds.join(',') });
  const response = await request<SekaiInfoResponse>(
    `${SEKAI_API_BASE_URL}/sekai/getOuterStreamInfo?${params.toString()}`
  );
  return response.data;
};

/**
 * 获取 Banner 内容
 * @param page 页码
 * @param size 每页数量
 */
export const fetchBanners = async (page: number = 1, size: number = 10): Promise<CampaignBannerItem[]> => {
  const params = new URLSearchParams({
    type: 'sekai_campaign_banner',
    page: page.toString(),
    size: size.toString(),
  });
  const response = await request<CampaignBannerResponse>(
    `${DISCOVER_API_BASE_URL}/page/config/by-type?${params.toString()}`
  );
  return response.data.items;
};

/**
 * 获取往期运营内容
 * @param page 页码
 * @param size 每页数量
 */
export const fetchCampaigns = async (page: number = 1, size: number = 10): Promise<CampaignBannerItem[]> => {
  const params = new URLSearchParams({
    type: 'sekai_campaign_past',
    page: page.toString(),
    size: size.toString(),
  });
  const response = await request<CampaignBannerResponse>(
    `${DISCOVER_API_BASE_URL}/page/config/by-type?${params.toString()}`
  );
  return response.data.items;
};

/**
 * 获取 Fandom 信息
 * @param page 页码
 * @param size 每页数量
 */
export const fetchFandoms = async (page: number = 1, size: number = 10): Promise<FandomItem[]> => {
    const params = new URLSearchParams({
        page: page.toString(),
        size: size.toString(),
        tagScenario: 'browse',
    });
    const response = await request<FandomResponse>(
        `${SEKAI_API_BASE_URL}/tags/v2/listTags?${params.toString()}`
    );
    return response.data.items;
};