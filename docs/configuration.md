# ⚙️ 配置文件

项目配置文件的详细说明和模板。

## Next.js 配置 (`next.config.ts`)

```typescript
import type { NextConfig } from "next"

const nextConfig: NextConfig = {
  // 输出配置
  output: 'standalone',
  
  // 实验性功能
  experimental: {
    // 启用 React 19 特性
    reactCompiler: true,
  },
  
  // 构建优化
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: process.env.NODE_ENV === 'production',
  },
  
  // 图片优化
  images: {
    domains: ['example.com'],
    formats: ['image/webp', 'image/avif'],
  },
  
  // 环境变量
  env: {
    CUSTOM_KEY: process.env.CUSTOM_KEY,
  },
  
  // 重定向
  async redirects() {
    return [
      {
        source: '/old-path',
        destination: '/new-path',
        permanent: true,
      },
    ]
  },
  
  // 头部配置
  async headers() {
    return [
      {
        source: '/api/:path*',
        headers: [
          {
            key: 'Access-Control-Allow-Origin',
            value: '*',
          },
        ],
      },
    ]
  },
}

export default nextConfig
```

### 配置选项说明

- **output** - 构建输出模式，`standalone` 适合容器化部署
- **experimental** - 实验性功能开关
- **eslint** - ESLint 构建时行为配置
- **typescript** - TypeScript 构建时行为配置
- **images** - 图片优化配置
- **env** - 环境变量注入
- **redirects** - URL 重定向规则
- **headers** - HTTP 头部配置

## Tailwind CSS 配置 (`tailwind.config.ts`)

```typescript
import type { Config } from "tailwindcss"

const config: Config = {
  darkMode: ["class"],
  content: [
    './pages/**/*.{ts,tsx}',
    './components/**/*.{ts,tsx}',
    './app/**/*.{ts,tsx}',
    './src/**/*.{ts,tsx}',
  ],
  prefix: "",
  theme: {
    container: {
      center: true,
      padding: "2rem",
      screens: {
        "2xl": "1400px",
      },
    },
    extend: {
      colors: {
        border: "hsl(var(--border))",
        input: "hsl(var(--input))",
        ring: "hsl(var(--ring))",
        background: "hsl(var(--background))",
        foreground: "hsl(var(--foreground))",
        primary: {
          DEFAULT: "hsl(var(--primary))",
          foreground: "hsl(var(--primary-foreground))",
        },
        secondary: {
          DEFAULT: "hsl(var(--secondary))",
          foreground: "hsl(var(--secondary-foreground))",
        },
        destructive: {
          DEFAULT: "hsl(var(--destructive))",
          foreground: "hsl(var(--destructive-foreground))",
        },
        muted: {
          DEFAULT: "hsl(var(--muted))",
          foreground: "hsl(var(--muted-foreground))",
        },
        accent: {
          DEFAULT: "hsl(var(--accent))",
          foreground: "hsl(var(--accent-foreground))",
        },
        popover: {
          DEFAULT: "hsl(var(--popover))",
          foreground: "hsl(var(--popover-foreground))",
        },
        card: {
          DEFAULT: "hsl(var(--card))",
          foreground: "hsl(var(--card-foreground))",
        },
      },
      borderRadius: {
        lg: "var(--radius)",
        md: "calc(var(--radius) - 2px)",
        sm: "calc(var(--radius) - 4px)",
      },
      keyframes: {
        "accordion-down": {
          from: { height: "0" },
          to: { height: "var(--radix-accordion-content-height)" },
        },
        "accordion-up": {
          from: { height: "var(--radix-accordion-content-height)" },
          to: { height: "0" },
        },
      },
      animation: {
        "accordion-down": "accordion-down 0.2s ease-out",
        "accordion-up": "accordion-up 0.2s ease-out",
      },
    },
  },
  plugins: [require("tailwindcss-animate")],
} satisfies Config

export default config
```

### CSS 变量配置 (`src/app/globals.css`)

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 222.2 47.4% 11.2%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 222.2 84% 4.9%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 210 40% 98%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 212.7 26.8% 83.9%;
  }
}
```

## TypeScript 配置 (`tsconfig.json`)

```json
{
  "compilerOptions": {
    "target": "ES2017",
    "lib": ["dom", "dom.iterable", "esnext"],
    "allowJs": true,
    "skipLibCheck": true,
    "strict": true,
    "noEmit": true,
    "esModuleInterop": true,
    "module": "esnext",
    "moduleResolution": "bundler",
    "resolveJsonModule": true,
    "isolatedModules": true,
    "jsx": "preserve",
    "incremental": true,
    "plugins": [
      {
        "name": "next"
      }
    ],
    "paths": {
      "@/*": ["./src/*"]
    }
  },
  "include": ["next-env.d.ts", "**/*.ts", "**/*.tsx", ".next/types/**/*.ts"],
  "exclude": ["node_modules"]
}
```

### 路径别名配置

```json
{
  "paths": {
    "@/*": ["./src/*"],
    "@/components/*": ["./src/components/*"],
    "@/lib/*": ["./src/lib/*"],
    "@/hooks/*": ["./src/hooks/*"],
    "@/stores/*": ["./src/stores/*"],
    "@/types/*": ["./src/types/*"],
    "@/utils/*": ["./src/lib/utils/*"]
  }
}
```

## ESLint 配置 (`eslint.config.mjs`)

```javascript
import { FlatCompat } from '@eslint/eslintrc'
import js from '@eslint/js'
import path from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = path.dirname(__filename)

const compat = new FlatCompat({
  baseDirectory: __dirname,
  recommendedConfig: js.configs.recommended,
})

const eslintConfig = [
  ...compat.config({
    extends: ['next/core-web-vitals', 'next/typescript'],
    rules: {
      '@typescript-eslint/no-unused-vars': ['warn', { 
        argsIgnorePattern: '^_',
        varsIgnorePattern: '^_',
      }],
      '@typescript-eslint/no-explicit-any': 'warn',
      'react-hooks/rules-of-hooks': 'error',
      'react-hooks/exhaustive-deps': 'warn',
    },
  }),
]

export default eslintConfig
```

## 环境变量配置 (`env.example`)

```bash
# ===========================================
# 🌐 应用配置
# ===========================================
NEXT_PUBLIC_APP_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:3000/api
NEXT_PUBLIC_NODE_ENV=development

# ===========================================
# 🔌 API 服务配置
# ===========================================
NEXT_PUBLIC_SEKAI_API_BASE_URL=http://127.0.0.1:7000
NEXT_PUBLIC_DISCOVER_API_BASE_URL=http://localhost:7700/api/v1/discover

# ===========================================
# 📊 分析配置
# ===========================================
NEXT_PUBLIC_POSTHOG_KEY=your_posthog_key_here
NEXT_PUBLIC_POSTHOG_HOST=https://app.posthog.com

# ===========================================
# 🔒 认证配置
# ===========================================
NEXTAUTH_SECRET=your_nextauth_secret_here
NEXTAUTH_URL=http://localhost:3000

# ===========================================
# 🗄️ 数据库配置
# ===========================================
DATABASE_URL=postgresql://username:password@localhost:5432/database

# ===========================================
# 🌉 代理配置 (可选)
# ===========================================
USE_PROXY=false
PROXY_HOST=127.0.0.1
PROXY_PORT=7890
HTTP_PROXY=http://127.0.0.1:7890
HTTPS_PROXY=http://127.0.0.1:7890
```

### 环境变量使用

```typescript
// 客户端使用（需要 NEXT_PUBLIC_ 前缀）
const apiUrl = process.env.NEXT_PUBLIC_API_URL

// 服务端使用
const databaseUrl = process.env.DATABASE_URL

// 类型定义
declare global {
  namespace NodeJS {
    interface ProcessEnv {
      NEXT_PUBLIC_APP_URL: string
      NEXT_PUBLIC_API_URL: string
      NEXT_PUBLIC_NODE_ENV: 'development' | 'production' | 'test'
      NEXT_PUBLIC_SEKAI_API_BASE_URL?: string
      NEXT_PUBLIC_DISCOVER_API_BASE_URL?: string
      NEXT_PUBLIC_POSTHOG_KEY?: string
      NEXT_PUBLIC_POSTHOG_HOST?: string
      NEXTAUTH_SECRET: string
      NEXTAUTH_URL: string
      DATABASE_URL: string
      USE_PROXY?: string
      PROXY_HOST?: string
      PROXY_PORT?: string
      HTTP_PROXY?: string
      HTTPS_PROXY?: string
    }
  }
}
```

## 依赖包配置 (`package.json`)

```json
{
  "name": "your-project-name",
  "version": "0.1.0",
  "private": true,
  "packageManager": "pnpm@9.0.0",
  "scripts": {
    "dev": "next dev",
    "build": "next build",
    "start": "next start",
    "lint": "next lint",
    "type-check": "tsc --noEmit",
    "clean": "rm -rf .next out dist",
    "clean:deps": "rm -rf node_modules pnpm-lock.yaml && pnpm install"
  },
  "dependencies": {
    "@radix-ui/react-slot": "^1.1.0",
    "class-variance-authority": "^0.7.0",
    "clsx": "^2.0.0",
    "framer-motion": "^12.14.0",
    "lucide-react": "^0.460.0",
    "next": "^15.3.2",
    "next-themes": "^0.2.1",
    "posthog-js": "^1.248.1",
    "react": "^19.0.0",
    "react-dom": "^19.0.0",
    "sonner": "^2.0.3",
    "tailwind-merge": "^2.2.0",
    "tailwindcss": "^3.4.1",
    "tailwindcss-animate": "^1.0.7",
    "zod": "^3.22.4",
    "zustand": "^4.4.7"
  },
  "devDependencies": {
    "@eslint/eslintrc": "^3",
    "@types/node": "^20",
    "@types/react": "^19",
    "@types/react-dom": "^19",
    "eslint": "^9",
    "eslint-config-next": "15.1.8",
    "postcss": "^8",
    "typescript": "^5"
  }
}
```

### 脚本说明

- **dev** - 启动开发服务器
- **build** - 构建生产版本
- **start** - 启动生产服务器
- **lint** - 运行 ESLint 检查
- **type-check** - 运行 TypeScript 类型检查
- **clean** - 清理构建产物
- **clean:deps** - 清理并重新安装依赖

## PostCSS 配置 (`postcss.config.mjs`)

```javascript
/** @type {import('postcss-load-config').Config} */
const config = {
  plugins: {
    tailwindcss: {},
    autoprefixer: {},
  },
}

export default config
```

## 组件配置 (`components.json`)

```json
{
  "$schema": "https://ui.shadcn.com/schema.json",
  "style": "default",
  "rsc": true,
  "tsx": true,
  "tailwind": {
    "config": "tailwind.config.ts",
    "css": "src/app/globals.css",
    "baseColor": "slate",
    "cssVariables": true,
    "prefix": ""
  },
  "aliases": {
    "components": "@/components",
    "utils": "@/lib/utils"
  }
}
```

## 最佳实践

### 1. 环境变量管理

- 使用 `.env.local` 存储本地开发环境变量
- 使用 `.env.production` 存储生产环境变量
- 敏感信息不要提交到版本控制
- 客户端变量必须以 `NEXT_PUBLIC_` 开头

### 2. TypeScript 配置

- 启用 `strict` 模式确保类型安全
- 配置路径别名简化导入
- 使用 `incremental` 提升编译速度

### 3. 构建优化

- 使用 `standalone` 输出减小部署体积
- 配置图片优化提升加载性能
- 合理配置 ESLint 规则

### 4. 样式配置

- 使用 CSS 变量支持主题切换
- 配置 Tailwind 插件扩展功能
- 优化 PurgeCSS 配置减小 CSS 体积 