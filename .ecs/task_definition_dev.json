{"containerDefinitions": [{"name": "sekai-app-discover-page-dev", "image": "010526279899.dkr.ecr.us-east-1.amazonaws.com/sekai-app-discover-page-dev-ecr:latest", "cpu": 0, "portMappings": [{"name": "3000", "containerPort": 3000, "hostPort": 3000, "protocol": "tcp", "appProtocol": "http"}], "essential": true, "environment": [{"name": "NEXT_PUBLIC_DISCOVER_API_BASE_URL", "value": "https://develop-api.sekai.chat/v3/discover"}, {"name": "NEXT_PUBLIC_SEKAI_API_BASE_URL", "value": "https://develop-api.sekai.chat"}], "environmentFiles": [], "mountPoints": [], "volumesFrom": [], "ulimits": [], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/sekai-app-discover-page-dev-task", "mode": "non-blocking", "awslogs-create-group": "true", "max-buffer-size": "25m", "awslogs-region": "us-east-1", "awslogs-stream-prefix": "ecs"}, "secretOptions": []}, "systemControls": []}], "family": "sekai-app-discover-page-dev-task", "taskRoleArn": "arn:aws:iam::010526279899:role/ecsTaskExecutionRole", "executionRoleArn": "arn:aws:iam::010526279899:role/ecsTaskExecutionRole", "networkMode": "awsvpc", "placementConstraints": [], "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "runtimePlatform": {"cpuArchitecture": "X86_64", "operatingSystemFamily": "LINUX"}, "tags": []}