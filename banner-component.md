# 轮播横幅组件 (Banner Carousel Component)

一个专为移动端设计的 React 轮播横幅组件，支持触摸滑动、自动播放和指示器导航。

## 特性

- ✅ 触摸滑动支持
- ✅ 自动播放功能
- ✅ 底部指示器导航
- ✅ 响应式设计
- ✅ TypeScript 支持
- ✅ 可自定义高度和播放间隔

## 技术栈

- React 19
- Next.js 15
- TypeScript
- Tailwind CSS
- clsx (条件类名)
- tailwind-merge (类名合并)

## 快速开始

### 1. 创建 Next.js 项目

```bash
npx create-next-app@latest banner-carousel --typescript --tailwind --eslint --app
cd banner-carousel
```

### 2. 安装依赖

```bash
npm install clsx tailwind-merge
# 或者使用 pnpm
pnpm add clsx tailwind-merge
```

### 3. 创建工具函数

创建 `lib/utils.ts` 文件：

```typescript
import { clsx, type ClassValue } from "clsx"
import { twMerge } from "tailwind-merge"

export function cn(...inputs: ClassValue[]) {
  return twMerge(clsx(inputs))
}
```

### 4. 创建轮播组件

创建 `components/banner.tsx` 文件：

```typescript
"use client"

import type React from "react"
import { useState, useEffect, useRef } from "react"
import Image from "next/image"

interface BannerItem {
  id: number
  image: string
  alt: string
}

interface BannerProps {
  items?: BannerItem[]
  autoPlay?: boolean
  autoPlayInterval?: number
  height?: number
}

const defaultItems: BannerItem[] = [
  {
    id: 1,
    image: "/images/banner1.png",
    alt: "Banner 1",
  },
  {
    id: 2,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 2",
  },
  {
    id: 3,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 3",
  },
  {
    id: 4,
    image: "/placeholder.svg?height=200&width=350",
    alt: "Banner 4",
  },
]

export default function Banner({
  items = defaultItems,
  autoPlay = true,
  autoPlayInterval = 3000,
  height = 200,
}: BannerProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isAutoPlaying, setIsAutoPlaying] = useState(autoPlay)
  const touchStartX = useRef<number>(0)
  const touchEndX = useRef<number>(0)
  const containerRef = useRef<HTMLDivElement>(null)

  // 自动播放
  useEffect(() => {
    if (!isAutoPlaying) return

    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex === items.length - 1 ? 0 : prevIndex + 1))
    }, autoPlayInterval)

    return () => clearInterval(interval)
  }, [isAutoPlaying, items.length, autoPlayInterval])

  // 处理触摸开始
  const handleTouchStart = (e: React.TouchEvent) => {
    touchStartX.current = e.targetTouches[0].clientX
    setIsAutoPlaying(false)
  }

  // 处理触摸移动
  const handleTouchMove = (e: React.TouchEvent) => {
    touchEndX.current = e.targetTouches[0].clientX
  }

  // 处理触摸结束
  const handleTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return

    const distance = touchStartX.current - touchEndX.current
    const isLeftSwipe = distance > 50
    const isRightSwipe = distance < -50

    if (isLeftSwipe) {
      goToNext()
    } else if (isRightSwipe) {
      goToPrevious()
    }

    // 重新启动自动播放
    setTimeout(() => setIsAutoPlaying(autoPlay), 2000)
  }

  // 下一张
  const goToNext = () => {
    setCurrentIndex((prevIndex) => (prevIndex === items.length - 1 ? 0 : prevIndex + 1))
  }

  // 上一张
  const goToPrevious = () => {
    setCurrentIndex((prevIndex) => (prevIndex === 0 ? items.length - 1 : prevIndex - 1))
  }

  // 跳转到指定索引
  const goToSlide = (index: number) => {
    setCurrentIndex(index)
    setIsAutoPlaying(false)
    setTimeout(() => setIsAutoPlaying(autoPlay), 2000)
  }

  return (
    <div className="relative w-full mx-auto bg-gray-100 rounded-lg overflow-hidden shadow-lg">
      {/* 轮播容器 */}
      <div
        ref={containerRef}
        className="relative overflow-hidden"
        style={{ height: `${height}px` }}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        {/* 轮播内容 */}
        <div
          className="flex transition-transform duration-300 ease-in-out h-full"
          style={{
            transform: `translateX(-${currentIndex * 100}%)`,
            width: `${items.length * 100}%`,
          }}
        >
          {items.map((item) => (
            <div
              key={item.id}
              className="relative flex-shrink-0 w-full h-full"
              style={{ width: `${100 / items.length}%` }}
            >
              <Image
                src={item.image || "/placeholder.svg"}
                alt={item.alt}
                fill
                className="object-cover"
                sizes="100vw"
                priority={item.id === 1}
              />
            </div>
          ))}
        </div>
      </div>

      {/* 底部指示器 */}
      <div className="absolute bottom-3 left-1/2 -translate-x-1/2 flex space-x-2">
        {items.map((_, index) => (
          <button
            key={index}
            onClick={() => goToSlide(index)}
            className={`w-2 h-2 rounded-full transition-all duration-200 ${
              index === currentIndex ? "bg-white scale-125" : "bg-white/50 hover:bg-white/75"
            }`}
            aria-label={`跳转到第${index + 1}张`}
          />
        ))}
      </div>
    </div>
  )
}
```

### 5. 创建演示页面

创建 `banner-demo.tsx` 文件：

```typescript
import Banner from "./components/banner"

export default function BannerDemo() {
  // 自定义轮播数据
  const bannerItems = [
    {
      id: 1,
      image: "/images/banner1.png",
      alt: "自定义横幅 1",
    },
    {
      id: 2,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 2",
    },
    {
      id: 3,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 3",
    },
    {
      id: 4,
      image: "/placeholder.svg?height=200&width=350",
      alt: "横幅 4",
    },
  ]

  return (
    <div className="min-h-screen bg-gray-50 p-4">
      <div className="max-w-md mx-auto space-y-6">
        <h1 className="text-2xl font-bold text-center text-gray-800">轮播横幅组件</h1>

        {/* 默认配置的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">默认横幅</h2>
          <Banner />
        </div>

        {/* 自定义高度的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">自定义高度 (150px)</h2>
          <Banner items={bannerItems} height={150} autoPlayInterval={4000} />
        </div>

        {/* 关闭自动播放的横幅 */}
        <div>
          <h2 className="text-lg font-semibold mb-3 text-gray-700">手动控制</h2>
          <Banner items={bannerItems} autoPlay={false} height={180} />
        </div>

        {/* 使用说明 */}
        <div className="bg-white p-4 rounded-lg shadow">
          <h3 className="font-semibold mb-2 text-gray-800">使用说明：</h3>
          <ul className="text-sm text-gray-600 space-y-1">
            <li>• 可以左右滑动切换</li>
            <li>• 点击底部指示器快速跳转</li>
            <li>• 支持自动播放功能</li>
            <li>• 图片会自动缩放适应容器</li>
          </ul>
        </div>
      </div>
    </div>
  )
}
```

### 6. 更新主页面

修改 `app/page.tsx`：

```typescript
import BannerDemo from "../banner-demo"

export default function Page() {
  return <BannerDemo />
}
```

### 7. 简化样式文件

修改 `app/globals.css`：

```css
@tailwind base;
@tailwind components;
@tailwind utilities;

body {
  font-family: Arial, Helvetica, sans-serif;
}
```

### 8. 简化 Tailwind 配置

修改 `tailwind.config.ts`：

```typescript
import type { Config } from "tailwindcss";

const config: Config = {
  content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
    extend: {},
  },
  plugins: [],
};
export default config;
```

## API 参考

### Banner 组件属性

| 属性 | 类型 | 默认值 | 描述 |
|------|------|--------|------|
| `items` | `BannerItem[]` | `defaultItems` | 轮播项目数组 |
| `autoPlay` | `boolean` | `true` | 是否自动播放 |
| `autoPlayInterval` | `number` | `3000` | 自动播放间隔（毫秒） |
| `height` | `number` | `200` | 轮播容器高度（像素） |

### BannerItem 接口

```typescript
interface BannerItem {
  id: number      // 唯一标识符
  image: string   // 图片 URL
  alt: string     // 图片描述文本
}
```

## 使用示例

### 基础用法

```typescript
import Banner from "./components/banner"

export default function MyPage() {
  return (
    <div>
      <Banner />
    </div>
  )
}
```

### 自定义配置

```typescript
import Banner from "./components/banner"

export default function MyPage() {
  const customItems = [
    { id: 1, image: "/image1.jpg", alt: "图片1" },
    { id: 2, image: "/image2.jpg", alt: "图片2" },
    { id: 3, image: "/image3.jpg", alt: "图片3" },
  ]

  return (
    <div>
      <Banner
        items={customItems}
        height={250}
        autoPlay={true}
        autoPlayInterval={5000}
      />
    </div>
  )
}
```

## 运行项目

```bash
npm run dev
# 或者
pnpm dev
```

访问 `http://localhost:3000` 查看效果。

## 构建项目

```bash
npm run build
# 或者
pnpm build
```

## 自定义样式

如果需要自定义样式，可以通过以下方式：

### 修改容器样式

```typescript
// 在 Banner 组件中修改容器的 className
<div className="relative w-full mx-auto bg-blue-100 rounded-xl overflow-hidden shadow-xl">
```

### 修改指示器样式

```typescript
// 自定义指示器颜色和大小
<button
  className={`w-3 h-3 rounded-full transition-all duration-200 ${
    index === currentIndex ? "bg-blue-500 scale-125" : "bg-blue-300 hover:bg-blue-400"
  }`}
/>
```

## 故障排除

### 常见问题

1. **图片不显示**
   - 检查图片路径是否正确
   - 确保图片文件存在于 `public` 目录中
   - 检查 Next.js 的图片优化配置

2. **触摸滑动不工作**
   - 确保在移动设备或浏览器开发者工具的移动模式下测试
   - 检查是否有其他元素阻止了触摸事件

3. **自动播放不工作**
   - 检查 `autoPlay` 属性是否设置为 `true`
   - 确保 `autoPlayInterval` 值合理（建议 > 1000ms）

### 调试技巧

```typescript
// 添加调试日志
const handleTouchStart = (e: React.TouchEvent) => {
  console.log('Touch start:', e.targetTouches[0].clientX)
  touchStartX.current = e.targetTouches[0].clientX
  setIsAutoPlaying(false)
}
```

