"use client"

import { useState, useRef, useEffect } from "react"
import { Search, ChevronRight, Eye } from "lucide-react"
import Image from "next/image"

const bannerItems = [
  {
    id: 1,
    title: "TOXIC ROMANCE",
    subtitle: "Your toxic bf or gf, can you handle their extreme love?",
    image: "/images/discover-banner.png",
    bgColor: "from-purple-600 to-pink-500",
  },
  {
    id: 2,
    title: "FANTASY WORLD",
    subtitle: "Enter a magical realm of endless possibilities",
    image: "/placeholder.svg?height=200&width=400",
    bgColor: "from-blue-600 to-cyan-500",
  },
  {
    id: 3,
    title: "MYSTERY NIGHT",
    subtitle: "Uncover secrets in the shadows of the night",
    image: "/placeholder.svg?height=200&width=400",
    bgColor: "from-gray-800 to-purple-900",
  },
]

const fandomTags = [
  { name: "<PERSON><PERSON><PERSON>", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-emerald-600" },
  { name: "Dragon ball Z", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-purple-600" },
  { name: "Doraemon", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-blue-600" },
  { name: "Inyasha", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-gray-600" },
  { name: "Naruto", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-orange-600" },
  { name: "One Piece", image: "/placeholder.svg?height=80&width=80", bgColor: "bg-red-600" },
]

const contentSections = [
  {
    title: "Your Toxic Ex",
    subtitle: "They cheated on you and they took your dog.....",
    items: [
      {
        id: 1,
        title: "Xiao",
        description: "Chisa is autistic and wild I want...",
        image: "/placeholder.svg?height=200&width=150",
        views: 228,
        gradient: "from-orange-500/80 to-red-600/80",
      },
      {
        id: 2,
        title: "Xiao",
        description: "Chisa is autistic and wild I want...",
        image: "/placeholder.svg?height=200&width=150",
        views: 228,
        gradient: "from-pink-500/80 to-purple-600/80",
      },
      {
        id: 3,
        title: "Kai",
        description: "Dark and mysterious character...",
        image: "/placeholder.svg?height=200&width=150",
        views: 156,
        gradient: "from-blue-500/80 to-indigo-600/80",
      },
      {
        id: 4,
        title: "Alex",
        description: "Charming but dangerous...",
        image: "/placeholder.svg?height=200&width=150",
        views: 342,
        gradient: "from-green-500/80 to-teal-600/80",
      },
      {
        id: 5,
        title: "More",
        description: "Click for details to view more sekai",
        isMore: true,
      },
    ],
  },
  {
    title: "Hot Guys Only",
    subtitle: "For girls who love spice",
    items: [
      {
        id: 1,
        title: "Xiao",
        description: "Chisa is autistic and wild I want...",
        image: "/placeholder.svg?height=200&width=150",
        views: 298,
        gradient: "from-cyan-500/80 to-blue-600/80",
      },
      {
        id: 2,
        title: "Xiao",
        description: "Chisa is autistic and wild I want...",
        image: "/placeholder.svg?height=200&width=150",
        views: 228,
        gradient: "from-yellow-500/80 to-orange-600/80",
      },
      {
        id: 3,
        title: "Ryan",
        description: "Hot and mysterious guy...",
        image: "/placeholder.svg?height=200&width=150",
        views: 445,
        gradient: "from-purple-500/80 to-pink-600/80",
      },
      {
        id: 4,
        title: "Jake",
        description: "Irresistible charm...",
        image: "/placeholder.svg?height=200&width=150",
        views: 367,
        gradient: "from-red-500/80 to-rose-600/80",
      },
      {
        id: 5,
        title: "More",
        description: "Click for details to view more sekai",
        isMore: true,
      },
    ],
  },
  {
    title: "Midnight Confessions",
    subtitle: "Dark secrets revealed in the shadows",
    items: [
      {
        id: 1,
        title: "Kai",
        description: "He whispers secrets in the dark...",
        image: "/placeholder.svg?height=200&width=150",
        views: 189,
        gradient: "from-indigo-500/80 to-purple-600/80",
      },
      {
        id: 2,
        title: "Ren",
        description: "Mysterious past haunts him...",
        image: "/placeholder.svg?height=200&width=150",
        views: 267,
        gradient: "from-gray-500/80 to-black/80",
      },
      {
        id: 3,
        title: "Zane",
        description: "Cold exterior, burning heart...",
        image: "/placeholder.svg?height=200&width=150",
        views: 334,
        gradient: "from-blue-500/80 to-cyan-600/80",
      },
      {
        id: 4,
        title: "Leo",
        description: "Dangerous charm and deadly smile...",
        image: "/placeholder.svg?height=200&width=150",
        views: 412,
        gradient: "from-red-500/80 to-pink-600/80",
      },
      {
        id: 5,
        title: "More",
        description: "Click for details to view more sekai",
        isMore: true,
      },
    ],
  },
  {
    title: "Forbidden Love",
    subtitle: "Love that breaks all the rules",
    items: [
      {
        id: 1,
        title: "Adrian",
        description: "Teacher's pet with a secret...",
        image: "/placeholder.svg?height=200&width=150",
        views: 523,
        gradient: "from-emerald-500/80 to-teal-600/80",
      },
      {
        id: 2,
        title: "Dante",
        description: "Boss's son, off limits...",
        image: "/placeholder.svg?height=200&width=150",
        views: 445,
        gradient: "from-amber-500/80 to-orange-600/80",
      },
      {
        id: 3,
        title: "Victor",
        description: "Best friend's brother...",
        image: "/placeholder.svg?height=200&width=150",
        views: 378,
        gradient: "from-violet-500/80 to-purple-600/80",
      },
      {
        id: 4,
        title: "Mason",
        description: "Rival turned lover...",
        image: "/placeholder.svg?height=200&width=150",
        views: 289,
        gradient: "from-rose-500/80 to-red-600/80",
      },
      {
        id: 5,
        title: "More",
        description: "Click for details to view more sekai",
        isMore: true,
      },
    ],
  },
  {
    title: "Bad Boys Club",
    subtitle: "Rebels with a cause and attitude",
    items: [
      {
        id: 1,
        title: "Ryder",
        description: "Motorcycle gang leader...",
        image: "/placeholder.svg?height=200&width=150",
        views: 612,
        gradient: "from-slate-500/80 to-gray-600/80",
      },
      {
        id: 2,
        title: "Jax",
        description: "Street fighter with heart...",
        image: "/placeholder.svg?height=200&width=150",
        views: 534,
        gradient: "from-zinc-500/80 to-stone-600/80",
      },
      {
        id: 3,
        title: "Knox",
        description: "Tattooed bad boy next door...",
        image: "/placeholder.svg?height=200&width=150",
        views: 467,
        gradient: "from-neutral-500/80 to-gray-700/80",
      },
      {
        id: 4,
        title: "Axel",
        description: "Dangerous smile, gentle touch...",
        image: "/placeholder.svg?height=200&width=150",
        views: 389,
        gradient: "from-stone-500/80 to-slate-600/80",
      },
      {
        id: 5,
        title: "More",
        description: "Click for details to view more sekai",
        isMore: true,
      },
    ],
  },
]

export default function DiscoverPage() {
  const [currentBanner, setCurrentBanner] = useState(0)
  const bannerRef = useRef<HTMLDivElement>(null)
  const fandomRef = useRef<HTMLDivElement>(null)

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentBanner((prev) => (prev + 1) % bannerItems.length)
    }, 5000)
    return () => clearInterval(timer)
  }, [])

  const scrollToNextBanner = (direction: "left" | "right") => {
    if (direction === "right") {
      setCurrentBanner((prev) => (prev + 1) % bannerItems.length)
    } else {
      setCurrentBanner((prev) => (prev - 1 + bannerItems.length) % bannerItems.length)
    }
  }

  return (
    <div className="min-h-screen bg-black text-white">
      {/* Header */}
      <div className="flex items-center justify-between p-4">
        <h1 className="text-2xl font-bold">Discover</h1>
        <Search className="w-6 h-6" />
      </div>

      {/* Carousel Banner */}
      <div className="relative mx-4 mb-6">
        <div
          ref={bannerRef}
          className="relative h-48 rounded-2xl overflow-hidden"
          onTouchStart={(e) => {
            const startX = e.touches[0].clientX
            const handleTouchEnd = (endEvent: TouchEvent) => {
              const endX = endEvent.changedTouches[0].clientX
              const diff = startX - endX
              if (Math.abs(diff) > 50) {
                scrollToNextBanner(diff > 0 ? "right" : "left")
              }
              document.removeEventListener("touchend", handleTouchEnd)
            }
            document.addEventListener("touchend", handleTouchEnd)
          }}
        >
          <div
            className="flex transition-transform duration-300 ease-in-out h-full"
            style={{ transform: `translateX(-${currentBanner * 100}%)` }}
          >
            {bannerItems.map((item) => (
              <div key={item.id} className="w-full h-full flex-shrink-0 relative">
                <div className={`absolute inset-0 bg-gradient-to-r ${item.bgColor} opacity-90`} />
                <Image src={item.image || "/placeholder.svg"} alt={item.title} fill className="object-cover" />
                <div className="absolute inset-0 p-4 flex flex-col justify-center">
                  <h2 className="text-2xl font-bold mb-2">{item.title}</h2>
                  <p className="text-sm opacity-90">{item.subtitle}</p>
                </div>
              </div>
            ))}
          </div>
          {/* Banner Indicators */}
          <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex space-x-2">
            {bannerItems.map((_, index) => (
              <button
                key={index}
                className={`w-2 h-2 rounded-full transition-colors ${
                  index === currentBanner ? "bg-white" : "bg-white/40"
                }`}
                onClick={() => setCurrentBanner(index)}
              />
            ))}
          </div>
        </div>
      </div>

      {/* Fandom Section */}
      <div className="mb-6">
        <div className="flex items-center justify-between px-4 mb-4">
          <div className="flex items-center">
            <span className="text-orange-500 mr-2">🔥</span>
            <h2 className="text-xl font-semibold">Fandom</h2>
          </div>
          <ChevronRight className="w-5 h-5 text-gray-400" />
        </div>

        <div
          ref={fandomRef}
          className="flex overflow-x-auto scrollbar-hide px-4 space-x-3"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          <div className="grid grid-rows-2 grid-flow-col gap-3 min-w-max">
            {fandomTags.map((tag, index) => (
              <div
                key={index}
                className={`${tag.bgColor} rounded-xl p-3 flex items-center space-x-3 min-w-[140px] h-16`}
              >
                <Image
                  src={tag.image || "/placeholder.svg"}
                  alt={tag.name}
                  width={40}
                  height={40}
                  className="rounded-lg object-cover"
                />
                <span className="text-sm font-medium text-white">{tag.name}</span>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Content Sections */}
      {contentSections.map((section, sectionIndex) => (
        <div key={sectionIndex} className="mb-8">
          <div className="flex items-center justify-between px-4 mb-4">
            <div>
              <h2 className="text-xl font-semibold">{section.title}</h2>
              <p className="text-sm text-gray-400">{section.subtitle}</p>
            </div>
            <ChevronRight className="w-5 h-5 text-gray-400" />
          </div>

          <div className="flex overflow-x-auto scrollbar-hide px-4 space-x-3">
            {section.items.map((item) => (
              <div key={item.id} className="flex-shrink-0">
                {item.isMore ? (
                  <div className="w-32 h-48 bg-gray-800 rounded-xl flex items-center justify-center p-4">
                    <p className="text-xs text-center text-gray-300">{item.description}</p>
                  </div>
                ) : (
                  <div className="relative w-32 h-48 rounded-xl overflow-hidden">
                    <Image src={item.image || "/placeholder.svg"} alt={item.title} fill className="object-cover" />
                    <div className={`absolute inset-0 bg-gradient-to-t ${item.gradient}`} />

                    {/* View count */}
                    <div className="absolute top-2 right-2 flex items-center bg-black/50 rounded-full px-2 py-1">
                      <Eye className="w-3 h-3 mr-1" />
                      <span className="text-xs">{item.views}</span>
                    </div>

                    {/* Content overlay */}
                    <div className="absolute bottom-0 left-0 right-0 p-3">
                      <h3 className="font-semibold text-sm mb-1">{item.title}</h3>
                      <p className="text-xs opacity-90 truncate">{item.description || ""}</p>
                    </div>
                  </div>
                )}
              </div>
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
